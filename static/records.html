<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>入住记录 - 宿舍入住管理系统</title>
  <link rel="stylesheet" href="css/common.css">
  <link rel="stylesheet" href="css/layout.css">
  <link rel="stylesheet" href="css/components.css">
  <style>
    /* 搜索表单样式 */
    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: end;
      margin-bottom: 20px;
    }

    .search-form .form-item {
      margin-bottom: 0;
      min-width: 120px;
    }

    .search-form .form-item label {
      display: block;
      margin-bottom: 4px;
      font-size: 13px;
      color: var(--text-color-regular);
    }

    .search-form .date-range {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .search-form .date-range input {
      width: 110px;
    }

    /* 表格样式 */
    .records-table {
      width: 100%;
      margin-top: 20px;
    }

    .records-table th,
    .records-table td {
      padding: 12px 8px;
      text-align: left;
      border-bottom: 1px solid var(--border-color-lighter);
    }

    .records-table th {
      background-color: #fafafa;
      font-weight: 600;
      color: var(--text-color-secondary);
    }

    .records-table tbody tr:hover {
      background-color: #f5f7fa;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }

    .action-buttons .btn {
      padding: 4px 8px;
      font-size: 12px;
      height: auto;
    }

    /* 对话框样式 */
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-row .form-item {
      flex: 1;
    }

    .form-textarea {
      min-height: 80px;
      resize: vertical;
    }

    /* 描述列表样式 */
    .descriptions {
      border: 1px solid var(--border-color-light);
      border-radius: var(--border-radius-base);
      overflow: hidden;
    }

    .descriptions-item {
      display: flex;
      border-bottom: 1px solid var(--border-color-lighter);
    }

    .descriptions-item:last-child {
      border-bottom: none;
    }

    .descriptions-label {
      background: #fafafa;
      padding: 12px 16px;
      font-weight: 500;
      color: var(--text-color-regular);
      min-width: 100px;
      border-right: 1px solid var(--border-color-lighter);
    }

    .descriptions-content {
      padding: 12px 16px;
      flex: 1;
      color: var(--text-color-primary);
    }

    /* 响应式 */
    @media (max-width: 768px) {
      .search-form {
        flex-direction: column;
        align-items: stretch;
      }

      .search-form .form-item {
        min-width: auto;
      }

      .records-table {
        font-size: 12px;
      }

      .action-buttons {
        flex-direction: column;
      }

      .form-row {
        flex-direction: column;
        gap: 0;
      }
    }

    /* 表格滚动 */
    .table-container {
      overflow-x: auto;
    }

    .records-table {
      min-width: 1000px;
    }
  </style>
</head>
<body>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <aside class="app-sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <h2>宿舍管理系统</h2>
        </div>
        <div class="logo-mini hidden">🏠</div>
      </div>
      
      <nav class="menu">
        <div class="menu-item">
          <a href="reports.html">
            <span class="menu-icon">📊</span>
            <span class="menu-title">报表统计</span>
          </a>
        </div>
        <div class="menu-item active">
          <a href="records.html">
            <span class="menu-icon">📋</span>
            <span class="menu-title">入住记录</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="departments.html">
            <span class="menu-icon">🏢</span>
            <span class="menu-title">部门管理</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="dormitories.html">
            <span class="menu-icon">🏠</span>
            <span class="menu-title">宿舍管理</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="residents.html">
            <span class="menu-icon">👤</span>
            <span class="menu-title">住户管理</span>
          </a>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <div class="app-main-container">
      <!-- 顶部导航 -->
      <header class="app-header">
        <div class="header-left">
          <button class="sidebar-toggle">📁</button>
          <nav class="breadcrumb">
            <div class="breadcrumb-item">
              <a href="reports.html">首页</a>
            </div>
            <div class="breadcrumb-item">
              <a href="records.html">入住记录</a>
            </div>
          </nav>
        </div>
        
        <div class="header-right">
          <button class="btn refresh-btn" title="刷新页面">🔄</button>

          <!-- 用户信息下拉菜单 -->
          <div class="user-info">
            <span>👤</span>
            <span class="username">用户</span>
            <span class="arrow-down">▼</span>
            
            <div class="dropdown-menu">
              <a href="#" class="dropdown-item" onclick="handleUserCommand('userInfo')">
                <span>👤</span>
                个人信息
              </a>
              <a href="#" class="dropdown-item divided" onclick="handleUserCommand('logout')">
                <span>🚪</span>
                退出登录
              </a>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容 -->
      <main class="app-main">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">入住记录</h1>
          <div class="page-actions">
            <button class="btn btn-primary" onclick="showCreateDialog()">
              <span>➕</span>
              新增记录
            </button>
            <button class="btn btn-success" onclick="showActiveRecords()">
              <span>👁️</span>
              活跃记录
            </button>
            <button class="btn" onclick="refreshData()">
              <span>🔄</span>
              刷新
            </button>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="card">
          <div class="card__body">
            <form class="search-form" id="searchForm">
              <div class="form-item">
                <label>宿舍</label>
                <select name="dormitory_id" class="input__inner">
                  <option value="">请选择宿舍</option>
                </select>
              </div>
              <div class="form-item">
                <label>部门</label>
                <select name="department_id" class="input__inner">
                  <option value="">请选择部门</option>
                </select>
              </div>
              <div class="form-item">
                <label>状态</label>
                <select name="status" class="input__inner">
                  <option value="">请选择状态</option>
                  <option value="ACTIVE">入住中</option>
                  <option value="COMPLETED">已离开</option>
                  <option value="CANCELLED">已取消</option>
                </select>
              </div>
              <div class="form-item">
                <label>入住日期</label>
                <div class="date-range">
                  <input type="date" name="start_date" class="input__inner">
                  <span>至</span>
                  <input type="date" name="end_date" class="input__inner">
                </div>
              </div>
              <div class="form-item">
                <button type="button" class="btn btn-primary" onclick="handleSearch()">
                  <span>🔍</span>
                  搜索
                </button>
                <button type="button" class="btn" onclick="resetSearch()">
                  <span>🔄</span>
                  重置
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="card">
          <div class="card__body">
            <div class="table-container">
              <table class="table records-table" id="recordsTable">
                <thead>
                  <tr>
                    <th style="width: 120px;">住户姓名</th>
                    <th style="width: 120px;">所属部门</th>
                    <th style="width: 120px;">项目组</th>
                    <th style="width: 120px;">宿舍</th>
                    <th style="width: 120px;">入住日期</th>
                    <th style="width: 120px;">离开日期</th>
                    <th style="width: 100px;">住宿天数</th>
                    <th style="width: 100px;">状态</th>
                    <th style="min-width: 150px;">备注</th>
                    <th style="width: 250px;">操作</th>
                  </tr>
                </thead>
                <tbody id="recordsTableBody">
                  <tr>
                    <td colspan="10" style="text-align: center; padding: 40px;">
                      <div class="loading">加载中...</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 移动端遮罩 -->
  <div class="sidebar-overlay"></div>

  <!-- 新增/编辑对话框 -->
  <div class="dialog-overlay" id="recordDialog" style="display: none;">
    <div class="dialog">
      <div class="dialog-header">
        <h3 id="dialogTitle">新增入住记录</h3>
      </div>
      <div class="dialog-body">
        <form class="form" id="recordForm">
          <div class="form-row">
            <div class="form-item">
              <label class="form-item__label">住户 *</label>
              <div class="form-item__content">
                <select name="resident_id" class="input__inner" required>
                  <option value="">请选择住户</option>
                </select>
              </div>
            </div>
            <div class="form-item">
              <label class="form-item__label">宿舍 *</label>
              <div class="form-item__content">
                <select name="dormitory_id" class="input__inner" required>
                  <option value="">请选择宿舍</option>
                </select>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-item__label">项目组</label>
              <div class="form-item__content">
                <input type="text" name="project_group" class="input__inner" placeholder="请输入项目组" maxlength="100">
              </div>
            </div>
            <div class="form-item">
              <label class="form-item__label">入住日期 *</label>
              <div class="form-item__content">
                <input type="date" name="check_in_date" class="input__inner" required>
              </div>
            </div>
          </div>
          <div class="form-item">
            <label class="form-item__label">备注</label>
            <div class="form-item__content">
              <textarea name="notes" class="input__inner form-textarea" placeholder="请输入备注信息" maxlength="500"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="dialog-footer">
        <button type="button" class="btn" onclick="closeRecordDialog()">取消</button>
        <button type="button" class="btn btn-primary" onclick="handleSubmit()" id="submitBtn">创建</button>
      </div>
    </div>
  </div>

  <!-- 办理离开对话框 -->
  <div class="dialog-overlay" id="checkoutDialog" style="display: none;">
    <div class="dialog">
      <div class="dialog-header">
        <h3>办理离开</h3>
      </div>
      <div class="dialog-body">
        <div class="descriptions" id="checkoutInfo">
          <!-- 住户信息将在这里显示 -->
        </div>
        <form class="form" id="checkoutForm" style="margin-top: 20px;">
          <div class="form-item">
            <label class="form-item__label">离开日期 *</label>
            <div class="form-item__content">
              <input type="date" name="checkout_date" class="input__inner" required>
            </div>
          </div>
          <div class="form-item">
            <label class="form-item__label">备注</label>
            <div class="form-item__content">
              <textarea name="notes" class="input__inner form-textarea" placeholder="请输入离开备注" maxlength="200"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="dialog-footer">
        <button type="button" class="btn" onclick="closeCheckoutDialog()">取消</button>
        <button type="button" class="btn btn-warning" onclick="handleCheckout()" id="checkoutBtn">确认离开</button>
      </div>
    </div>
  </div>

  <script src="js/utils.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/layout.js"></script>
  <script src="js/records.js"></script>
</body>
</html>
