<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>住户管理 - 宿舍入住管理系统</title>
  <link rel="stylesheet" href="css/common.css">
  <link rel="stylesheet" href="css/layout.css">
  <link rel="stylesheet" href="css/components.css">
</head>
<body>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <aside class="app-sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <h2>宿舍管理系统</h2>
        </div>
        <div class="logo-mini hidden">🏠</div>
      </div>
      
      <nav class="menu">
        <div class="menu-item">
          <a href="reports.html">
            <span class="menu-icon">📊</span>
            <span class="menu-title">报表统计</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="records.html">
            <span class="menu-icon">📋</span>
            <span class="menu-title">入住记录</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="departments.html">
            <span class="menu-icon">🏢</span>
            <span class="menu-title">部门管理</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="dormitories.html">
            <span class="menu-icon">🏠</span>
            <span class="menu-title">宿舍管理</span>
          </a>
        </div>
        <div class="menu-item active">
          <a href="residents.html">
            <span class="menu-icon">👤</span>
            <span class="menu-title">住户管理</span>
          </a>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <div class="app-main-container">
      <!-- 顶部导航 -->
      <header class="app-header">
        <div class="header-left">
          <button class="sidebar-toggle">📁</button>
          <nav class="breadcrumb">
            <div class="breadcrumb-item">
              <a href="reports.html">首页</a>
            </div>
            <div class="breadcrumb-item">
              <a href="residents.html">住户管理</a>
            </div>
          </nav>
        </div>
        
        <div class="header-right">
          <button class="btn refresh-btn" title="刷新页面">🔄</button>
          <div class="user-info">
            <span>👤</span>
            <span class="username">用户</span>
            <span class="arrow-down">▼</span>
            <div class="dropdown-menu">
              <a href="#" class="dropdown-item" onclick="handleUserCommand('userInfo')">
                <span>👤</span>
                个人信息
              </a>
              <a href="#" class="dropdown-item divided" onclick="handleUserCommand('logout')">
                <span>🚪</span>
                退出登录
              </a>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容 -->
      <main class="app-main">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">住户管理</h1>
          <div class="page-actions">
            <button class="btn btn-primary" onclick="showCreateDialog()">
              <span>➕</span>
              新增住户
            </button>
            <button class="btn" onclick="refreshData()">
              <span>🔄</span>
              刷新
            </button>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="card">
          <div class="card__body">
            <form class="search-form" id="searchForm" style="display: flex; gap: 16px; align-items: end; flex-wrap: wrap;">
              <div class="form-item">
                <label>姓名</label>
                <input type="text" name="name" class="input__inner" placeholder="请输入姓名">
              </div>
              <div class="form-item">
                <label>部门</label>
                <select name="department_id" class="input__inner">
                  <option value="">请选择部门</option>
                </select>
              </div>
              <div class="form-item">
                <button type="button" class="btn btn-primary" onclick="handleSearch()">
                  <span>🔍</span>
                  搜索
                </button>
                <button type="button" class="btn" onclick="resetSearch()">
                  <span>🔄</span>
                  重置
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="card">
          <div class="card__body">
            <div class="table-container">
              <table class="table" id="residentsTable">
                <thead>
                  <tr>
                    <th>姓名</th>
                    <th>员工号</th>
                    <th>所属部门</th>
                    <th>项目组</th>
                    <th>联系电话</th>
                    <th>邮箱</th>
                    <th>创建时间</th>
                    <th style="width: 200px;">操作</th>
                  </tr>
                </thead>
                <tbody id="residentsTableBody">
                  <tr>
                    <td colspan="8" style="text-align: center; padding: 40px;">
                      <div class="loading">加载中...</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 移动端遮罩 -->
  <div class="sidebar-overlay"></div>

  <!-- 新增/编辑对话框 -->
  <div class="dialog-overlay" id="residentDialog" style="display: none;">
    <div class="dialog">
      <div class="dialog-header">
        <h3 id="dialogTitle">新增住户</h3>
      </div>
      <div class="dialog-body">
        <form class="form" id="residentForm">
          <div style="display: flex; gap: 20px; margin-bottom: 20px;">
            <div class="form-item" style="flex: 1;">
              <label class="form-item__label">姓名 *</label>
              <div class="form-item__content">
                <input type="text" name="name" class="input__inner" placeholder="请输入姓名" maxlength="50" required>
              </div>
            </div>
            <div class="form-item" style="flex: 1;">
              <label class="form-item__label">员工号</label>
              <div class="form-item__content">
                <input type="text" name="employee_id" class="input__inner" placeholder="请输入员工号" maxlength="50">
              </div>
            </div>
          </div>
          <div style="display: flex; gap: 20px; margin-bottom: 20px;">
            <div class="form-item" style="flex: 1;">
              <label class="form-item__label">所属部门 *</label>
              <div class="form-item__content">
                <select name="department_id" class="input__inner" required>
                  <option value="">请选择部门</option>
                </select>
              </div>
            </div>
            <div class="form-item" style="flex: 1;">
              <label class="form-item__label">项目组</label>
              <div class="form-item__content">
                <input type="text" name="project_group" class="input__inner" placeholder="请输入项目组" maxlength="100">
              </div>
            </div>
          </div>
          <div style="display: flex; gap: 20px; margin-bottom: 20px;">
            <div class="form-item" style="flex: 1;">
              <label class="form-item__label">联系电话</label>
              <div class="form-item__content">
                <input type="tel" name="phone" class="input__inner" placeholder="请输入联系电话" maxlength="20">
              </div>
            </div>
            <div class="form-item" style="flex: 1;">
              <label class="form-item__label">邮箱</label>
              <div class="form-item__content">
                <input type="email" name="email" class="input__inner" placeholder="请输入邮箱" maxlength="100">
              </div>
            </div>
          </div>
          <div class="form-item">
            <label class="form-item__label">备注</label>
            <div class="form-item__content">
              <textarea name="notes" class="input__inner" placeholder="请输入备注信息" maxlength="500" style="min-height: 80px; resize: vertical;"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="dialog-footer">
        <button type="button" class="btn" onclick="closeResidentDialog()">取消</button>
        <button type="button" class="btn btn-primary" onclick="handleSubmit()" id="submitBtn">创建</button>
      </div>
    </div>
  </div>

  <script src="js/utils.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/layout.js"></script>
  <script>
    // 住户管理逻辑
    class ResidentsManager {
      constructor() {
        this.residents = [];
        this.departments = [];
        this.loading = false;
        this.currentResident = null;
        this.isEdit = false;
        this.searchForm = { name: '', department_id: '' };
        this.init();
      }

      async init() {
        await this.loadDepartments();
        await this.loadResidents();
      }

      async loadDepartments() {
        try {
          const response = await api.get('/v1/departments');
          this.departments = response.items || response;
          this.populateSearchOptions();
          this.populateFormOptions();
        } catch (error) {
          console.error('加载部门失败:', error);
        }
      }

      populateSearchOptions() {
        const departmentSelect = document.querySelector('#searchForm select[name="department_id"]');
        departmentSelect.innerHTML = '<option value="">请选择部门</option>';
        this.departments.forEach(dept => {
          departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
        });
      }

      populateFormOptions() {
        const departmentSelect = document.querySelector('#residentForm select[name="department_id"]');
        departmentSelect.innerHTML = '<option value="">请选择部门</option>';
        this.departments.forEach(dept => {
          departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
        });
      }

      async loadResidents() {
        this.loading = true;
        this.renderLoading();

        try {
          const params = new URLSearchParams();
          Object.entries(this.searchForm).forEach(([key, value]) => {
            if (value) params.append(key, value);
          });

          const response = await api.get(`/v1/residents?${params.toString()}`);
          this.residents = response.items || response;
          this.renderResidents();
        } catch (error) {
          console.error('加载住户失败:', error);
          this.renderError();
        } finally {
          this.loading = false;
        }
      }

      renderLoading() {
        const tbody = document.getElementById('residentsTableBody');
        tbody.innerHTML = `
          <tr>
            <td colspan="8" style="text-align: center; padding: 40px;">
              <div class="loading"></div>
              <div style="margin-top: 12px;">加载中...</div>
            </td>
          </tr>
        `;
      }

      renderResidents() {
        const tbody = document.getElementById('residentsTableBody');
        
        if (!this.residents || this.residents.length === 0) {
          tbody.innerHTML = `
            <tr>
              <td colspan="8" style="text-align: center; padding: 40px;">
                <div class="empty">
                  <div class="empty-description">暂无住户数据</div>
                </div>
              </td>
            </tr>
          `;
          return;
        }

        tbody.innerHTML = this.residents.map(resident => `
          <tr>
            <td>${resident.name}</td>
            <td>${resident.employee_id || '-'}</td>
            <td>${resident.department_name || '-'}</td>
            <td>${resident.project_group || '-'}</td>
            <td>${resident.phone || '-'}</td>
            <td>${resident.email || '-'}</td>
            <td>${formatDate(resident.created_at, 'YYYY-MM-DD HH:mm')}</td>
            <td>
              <div style="display: flex; gap: 8px;">
                <button class="btn btn-primary" onclick="showEditDialog('${resident.id}')" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                <button class="btn btn-danger" onclick="handleDelete('${resident.id}')" style="padding: 4px 8px; font-size: 12px;">删除</button>
              </div>
            </td>
          </tr>
        `).join('');
      }

      renderError() {
        const tbody = document.getElementById('residentsTableBody');
        tbody.innerHTML = `
          <tr>
            <td colspan="8" style="text-align: center; padding: 40px;">
              <div class="empty">
                <div class="empty-description">加载失败，请重试</div>
              </div>
            </td>
          </tr>
        `;
      }

      showCreateDialog() {
        this.isEdit = false;
        this.currentResident = null;
        document.getElementById('dialogTitle').textContent = '新增住户';
        document.getElementById('submitBtn').textContent = '创建';
        document.getElementById('residentForm').reset();
        document.getElementById('residentDialog').style.display = 'flex';
      }

      showEditDialog(residentId) {
        const resident = this.residents.find(r => r.id === residentId);
        if (!resident) {
          showMessage('住户不存在', 'error');
          return;
        }

        this.isEdit = true;
        this.currentResident = resident;
        document.getElementById('dialogTitle').textContent = '编辑住户';
        document.getElementById('submitBtn').textContent = '更新';

        const form = document.getElementById('residentForm');
        form.querySelector('input[name="name"]').value = resident.name;
        form.querySelector('input[name="employee_id"]').value = resident.employee_id || '';
        form.querySelector('select[name="department_id"]').value = resident.department_id || '';
        form.querySelector('input[name="project_group"]').value = resident.project_group || '';
        form.querySelector('input[name="phone"]').value = resident.phone || '';
        form.querySelector('input[name="email"]').value = resident.email || '';
        form.querySelector('textarea[name="notes"]').value = resident.notes || '';

        document.getElementById('residentDialog').style.display = 'flex';
      }

      closeResidentDialog() {
        document.getElementById('residentDialog').style.display = 'none';
        document.getElementById('residentForm').reset();
        this.currentResident = null;
        this.isEdit = false;
      }

      async handleSubmit() {
        const form = document.getElementById('residentForm');
        const formData = new FormData(form);

        const validator = new FormValidator(form);
        validator.addRule('name', [{ required: true, message: '请输入姓名' }]);
        validator.addRule('department_id', [{ required: true, message: '请选择部门' }]);

        const validation = validator.validate();
        if (!validation.valid) return;

        const submitBtn = document.getElementById('submitBtn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '提交中...';
        submitBtn.disabled = true;

        try {
          const data = {
            name: formData.get('name'),
            employee_id: formData.get('employee_id') || null,
            department_id: formData.get('department_id'),
            project_group: formData.get('project_group') || null,
            phone: formData.get('phone') || null,
            email: formData.get('email') || null,
            notes: formData.get('notes') || null
          };

          if (this.isEdit) {
            await api.put(`/v1/residents/${this.currentResident.id}`, data);
            showMessage('住户更新成功', 'success');
          } else {
            await api.post('/v1/residents', data);
            showMessage('住户创建成功', 'success');
          }

          this.closeResidentDialog();
          await this.loadResidents();
        } catch (error) {
          console.error('提交失败:', error);
          showMessage(error.message || '提交失败', 'error');
        } finally {
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        }
      }

      async handleDelete(residentId) {
        const resident = this.residents.find(r => r.id === residentId);
        if (!resident) {
          showMessage('住户不存在', 'error');
          return;
        }

        const confirmed = await showConfirm(`确定要删除住户"${resident.name}"吗？`, '删除确认');
        if (!confirmed) return;

        try {
          await api.delete(`/v1/residents/${residentId}`);
          showMessage('住户删除成功', 'success');
          await this.loadResidents();
        } catch (error) {
          console.error('删除失败:', error);
          showMessage(error.message || '删除失败', 'error');
        }
      }

      handleSearch() {
        const form = document.getElementById('searchForm');
        const formData = new FormData(form);
        this.searchForm = {
          name: formData.get('name') || '',
          department_id: formData.get('department_id') || ''
        };
        this.loadResidents();
      }

      resetSearch() {
        document.getElementById('searchForm').reset();
        this.searchForm = { name: '', department_id: '' };
        this.loadResidents();
      }

      async refreshData() {
        await this.loadDepartments();
        await this.loadResidents();
        showMessage('数据已刷新', 'success');
      }
    }

    // 全局函数
    function showCreateDialog() { window.residentsManager?.showCreateDialog(); }
    function showEditDialog(id) { window.residentsManager?.showEditDialog(id); }
    function closeResidentDialog() { window.residentsManager?.closeResidentDialog(); }
    function handleSubmit() { window.residentsManager?.handleSubmit(); }
    function handleDelete(id) { window.residentsManager?.handleDelete(id); }
    function handleSearch() { window.residentsManager?.handleSearch(); }
    function resetSearch() { window.residentsManager?.resetSearch(); }
    function refreshData() { window.residentsManager?.refreshData(); }

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      window.residentsManager = new ResidentsManager();
    });
  </script>
</body>
</html>
