/* 通用样式文件 - 替代Element Plus和全局样式 */

/* CSS变量定义 */
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  --text-color-placeholder: #c0c4cc;
  
  --border-color-base: #dcdfe6;
  --border-color-light: #e4e7ed;
  --border-color-lighter: #ebeef5;
  --border-color-extra-light: #f2f6fc;
  
  --bg-color: #ffffff;
  --bg-color-page: #f5f7fa;
  --bg-color-overlay: rgba(255, 255, 255, 0.9);
  
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-round: 20px;
  --border-radius-circle: 100%;
}

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 
               'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', 
               Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: var(--text-color-primary);
  background-color: var(--bg-color-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  height: 32px;
  white-space: nowrap;
  cursor: pointer;
  background: var(--bg-color);
  border: 1px solid var(--border-color-base);
  color: var(--text-color-regular);
  text-align: center;
  box-sizing: border-box;
  outline: none;
  margin: 0;
  font-weight: 500;
  padding: 8px 15px;
  font-size: 14px;
  border-radius: var(--border-radius-base);
  transition: 0.1s;
  text-decoration: none;
  user-select: none;
}

.btn:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: #ecf5ff;
}

.btn:active {
  color: #3a8ee6;
  border-color: #3a8ee6;
  outline: none;
}

.btn:focus {
  outline: none;
}

.btn.btn-primary {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn.btn-primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}

.btn.btn-success {
  color: #fff;
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn.btn-success:hover {
  background: #85ce61;
  border-color: #85ce61;
  color: #fff;
}

.btn.btn-warning {
  color: #fff;
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn.btn-warning:hover {
  background: #ebb563;
  border-color: #ebb563;
  color: #fff;
}

.btn.btn-danger {
  color: #fff;
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn.btn-danger:hover {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}

.btn.btn-large {
  height: 40px;
  padding: 12px 19px;
  font-size: 14px;
  border-radius: var(--border-radius-base);
}

.btn.btn-small {
  height: 24px;
  padding: 5px 11px;
  font-size: 12px;
  border-radius: 3px;
}

.btn:disabled {
  color: var(--text-color-placeholder);
  cursor: not-allowed;
  background-image: none;
  background-color: var(--bg-color);
  border-color: var(--border-color-lighter);
}

.btn.loading {
  position: relative;
  pointer-events: none;
}

.btn.loading:before {
  pointer-events: none;
  content: '';
  position: absolute;
  left: -1px;
  top: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: inherit;
  background-color: inherit;
}

/* 输入框样式 */
.input {
  position: relative;
  font-size: 14px;
  display: inline-block;
  width: 100%;
}

.input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: var(--border-radius-base);
  border: 1px solid var(--border-color-base);
  box-sizing: border-box;
  color: var(--text-color-regular);
  display: inline-block;
  font-size: inherit;
  height: 32px;
  line-height: 32px;
  outline: none;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}

.input__inner:focus {
  outline: none;
  border-color: var(--primary-color);
}

.input__inner::placeholder {
  color: var(--text-color-placeholder);
}

.input.input-large .input__inner {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

.input.input-small .input__inner {
  height: 24px;
  line-height: 24px;
  font-size: 13px;
}

/* 表单样式 */
.form {
  margin: 0;
  padding: 0;
}

.form-item {
  margin-bottom: 22px;
}

.form-item__label {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: var(--text-color-regular);
  line-height: 32px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 500;
}

.form-item__content {
  line-height: 32px;
  position: relative;
  font-size: 14px;
}

.form-item__error {
  color: var(--danger-color);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}

/* 卡片样式 */
.card {
  border-radius: var(--border-radius-base);
  border: 1px solid var(--border-color-lighter);
  background-color: var(--bg-color);
  overflow: hidden;
  color: var(--text-color-primary);
  transition: 0.3s;
  box-shadow: var(--box-shadow-light);
  margin-bottom: 20px;
}

.card__header {
  padding: 18px 20px;
  border-bottom: 1px solid var(--border-color-lighter);
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card__body {
  padding: 20px;
}

/* 表格样式 */
.table {
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
  border: 1px solid var(--border-color-base);
}

.table th,
.table td {
  padding: 12px 0;
  min-width: 0;
  box-sizing: border-box;
  text-overflow: ellipsis;
  vertical-align: middle;
  position: relative;
  text-align: left;
  border-bottom: 1px solid var(--border-color-lighter);
}

.table th {
  color: var(--text-color-secondary);
  font-weight: 500;
  background-color: #fafafa;
}

.table tbody tr:hover {
  background-color: #f5f7fa;
}

/* 消息提示样式 */
.message {
  min-width: 380px;
  box-sizing: border-box;
  border-radius: var(--border-radius-base);
  position: fixed;
  left: 50%;
  top: 20px;
  transform: translateX(-50%);
  background-color: #edf2fc;
  transition: opacity 0.3s, transform 0.4s, top 0.4s;
  overflow: hidden;
  padding: 15px 15px 15px 20px;
  display: flex;
  align-items: center;
  z-index: 9999;
}

.message.message-success {
  background-color: #f0f9ff;
  border-color: #c6f7d0;
  color: var(--success-color);
}

.message.message-warning {
  background-color: #fdf6ec;
  border-color: #f5dab1;
  color: var(--warning-color);
}

.message.message-error {
  background-color: #fef0f0;
  border-color: #fbc4c4;
  color: var(--danger-color);
}

.message.message-info {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: var(--info-color);
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.flex { display: flex; }
.flex-1 { flex: 1; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.items-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
