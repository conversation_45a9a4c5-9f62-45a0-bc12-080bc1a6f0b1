/* 布局相关样式 */

/* 应用布局 */
.app-layout {
  height: 100vh;
  display: flex;
}

/* 侧边栏样式 */
.app-sidebar {
  width: 200px;
  background-color: #304156;
  transition: width 0.3s;
  flex-shrink: 0;
}

.app-sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #434a50;
}

.sidebar-header .logo h2 {
  color: #ffffff;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.sidebar-header .logo-mini {
  color: #ffffff;
  font-size: 24px;
}

/* 菜单样式 */
.menu {
  list-style: none;
  margin: 0;
  padding: 0;
  background-color: #304156;
}

.menu-item {
  position: relative;
}

.menu-item a {
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: 56px;
  color: #bfcbd9;
  text-decoration: none;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.menu-item a:hover {
  background-color: #263445;
  color: #ffffff;
}

.menu-item.active a {
  background-color: #409eff;
  color: #ffffff;
  border-left-color: #66b1ff;
}

.menu-item .menu-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 18px;
  text-align: center;
  line-height: 24px;
}

.menu-item .menu-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.app-sidebar.collapsed .menu-item .menu-title {
  display: none;
}

.app-sidebar.collapsed .menu-item a {
  justify-content: center;
  padding: 0;
}

.app-sidebar.collapsed .menu-item .menu-icon {
  margin-right: 0;
}

/* 主内容区域 */
.app-main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部导航 */
.app-header {
  height: 60px;
  background-color: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: #606266;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.sidebar-toggle:hover {
  color: var(--primary-color);
  background-color: #f5f7fa;
}

/* 面包屑 */
.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 1;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin: 0 8px;
  color: var(--text-color-placeholder);
}

.breadcrumb-item a {
  color: var(--text-color-regular);
  text-decoration: none;
  transition: color 0.3s;
}

.breadcrumb-item a:hover {
  color: var(--primary-color);
}

.breadcrumb-item:last-child a {
  color: var(--text-color-primary);
  cursor: default;
}

.breadcrumb-item:last-child a:hover {
  color: var(--text-color-primary);
}

/* 头部右侧 */
.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-right .btn {
  color: #606266;
  border: none;
  background: none;
  padding: 8px;
}

.header-right .btn:hover {
  color: var(--primary-color);
  background-color: #f5f7fa;
}

/* 用户信息下拉 */
.user-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
  position: relative;
}

.user-info:hover {
  background-color: #f5f7fa;
  color: var(--primary-color);
}

.user-info .username {
  font-size: 14px;
  font-weight: 500;
}

.user-info .arrow-down {
  font-size: 12px;
  transition: transform 0.3s;
}

/* 下拉菜单 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-light);
  min-width: 120px;
  z-index: 1000;
  display: none;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: var(--text-color-regular);
  text-decoration: none;
  transition: all 0.3s;
  gap: 8px;
}

.dropdown-item:hover {
  background-color: #f5f7fa;
  color: var(--primary-color);
}

.dropdown-item.divided {
  border-top: 1px solid var(--border-color-lighter);
}

/* 主内容区 */
.app-main {
  flex: 1;
  background-color: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color-light);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-light);
  transition: transform 0.3s;
}

.stats-item:hover {
  transform: translateY(-2px);
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 16px;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-color-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: var(--text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    position: fixed;
    left: -200px;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s;
  }
  
  .app-sidebar.show {
    left: 0;
  }
  
  .app-main-container {
    margin-left: 0;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .app-main {
    padding: 10px;
  }
}

/* 遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

.sidebar-overlay.show {
  display: block;
}

/* 页面切换动画 */
.page-transition {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.3s ease;
}

.page-transition.show {
  opacity: 1;
  transform: translateX(0);
}
